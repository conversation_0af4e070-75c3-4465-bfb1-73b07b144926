//@version=6
indicator("Delta Heatmap Candles", overlay=true)

import TradingView/ta/8

// Input settings for timeframe selection
lowerTimeframeTooltip = "The indicator scans lower timeframe data to approximate up and down volume used in the delta calculation. By default, the timeframe is chosen automatically. These inputs override this with a custom timeframe.
 \n\nHigher timeframes provide more historical data, but the data will be less precise."
useCustomTimeframeInput = input.bool(false, "Use custom timeframe", tooltip = lowerTimeframeTooltip)
lowerTimeframeInput = input.timeframe("1", "Timeframe")

// Delta intensity settings
intensityMultiplier = input.float(1.0, "Delta Sensitivity", minval=0.1, maxval=5.0, step=0.1, tooltip="Adjust the sensitivity of delta detection. Higher values make smaller deltas more visible.")

// Color threshold display (for reference)
thresholdInfo = "Color Thresholds (based on Standard Deviation):\n• <0.5σ: White (Neutral)\n• 0.5-1.0σ: Light Blue/Red\n• 1.0-1.5σ: Blue/Red\n• >1.5σ: Dark Blue/Red\n\nSeparate calculations for bullish and bearish deltas"
showThresholds = input.bool(false, "Show Color Thresholds", tooltip=thresholdInfo)

// Determine the appropriate lower timeframe
var lowerTimeframe = switch
    useCustomTimeframeInput => lowerTimeframeInput
    timeframe.isseconds     => "1S"
    timeframe.isintraday    => "1"
    timeframe.isdaily       => "5"
    => "60"

// Get volume delta data
[openVolume, maxVolume, minVolume, lastVolume] = ta.requestVolumeDelta(lowerTimeframe)

// Length for standard deviation calculation
lookbackLength = input.int(100, "Lookback Length", minval=20, maxval=500, tooltip="Number of bars to use for standard deviation calculation")

// Separate positive and negative deltas for individual standard deviation calculations
positiveDelta = lastVolume > 0 ? lastVolume : 0
negativeDelta = lastVolume < 0 ? math.abs(lastVolume) : 0

// Calculate standard deviations for positive and negative deltas separately
positiveStdDev = ta.stdev(positiveDelta, lookbackLength)
negativeStdDev = ta.stdev(negativeDelta, lookbackLength)

// Calculate means for positive and negative deltas
positiveMean = ta.sma(positiveDelta, lookbackLength)
negativeMean = ta.sma(negativeDelta, lookbackLength)

// Normalize current delta based on its respective distribution
normalizedDelta = if lastVolume > 0 and positiveStdDev > 0
    (lastVolume - positiveMean) / positiveStdDev
else if lastVolume < 0 and negativeStdDev > 0
    (math.abs(lastVolume) - negativeMean) / negativeStdDev
else
    0

// Apply intensity multiplier and clamp to reasonable range
adjustedNormalizedDelta = math.abs(normalizedDelta * intensityMultiplier)
clampedDelta = math.min(adjustedNormalizedDelta, 3.0)  // Clamp to 3 standard deviations

// Define color thresholds and colors
// Bullish colors (positive delta)
lightBlue = color.new(color.blue, 70)    // Light blue (25-50%)
mediumBlue = color.new(color.blue, 30)   // Blue (50-75%)
darkBlue = color.new(color.blue, 0)      // Dark blue (75%+)

// Bearish colors (negative delta)
lightRed = color.new(color.red, 70)      // Light red (25-50%)
mediumRed = color.new(color.red, 30)     // Red (50-75%)
darkRed = color.new(color.red, 0)        // Dark red (75%+)

// Neutral color
neutralWhite = color.white

// Function to get color based on normalized standard deviation
getColorByStdDev(normalizedStdDev, isPositive) =>
    // Convert normalized std dev to percentage thresholds
    // 0.25 std dev = 25%, 0.75 std dev = 50%, 1.25 std dev = 75%, 1.75+ std dev = 100%
    stdDevPercent = normalizedStdDev / 2.0  // Scale to make thresholds more reasonable

    if stdDevPercent < 0.25
        neutralWhite  // Below 0.5 std dev = white/neutral
    else if stdDevPercent < 0.50
        isPositive ? lightBlue : lightRed  // 0.5-1.0 std dev = light color
    else if stdDevPercent < 0.75
        isPositive ? mediumBlue : mediumRed  // 1.0-1.5 std dev = medium color
    else
        isPositive ? darkBlue : darkRed  // 1.5+ std dev = dark color

// Determine candle color based on normalized standard deviation
candleColor = getColorByStdDev(clampedDelta, lastVolume > 0)

// Plot the colored candles
plotcandle(open, high, low, close, "Delta Heatmap", color=candleColor, wickcolor=candleColor, bordercolor=candleColor)

// Error handling for missing volume data
var cumVol = 0.
cumVol += nz(volume)
if barstate.islast and cumVol == 0
    runtime.error("The data vendor doesn't provide volume data for this symbol.")

// Optional: Add a small indicator to show delta value
plotchar(lastVolume, "Delta Value", "", location.top, size=size.tiny)