//@version=6
indicator("Delta Heatmap Candles", overlay=true)

import TradingView/ta/8

// Input settings for timeframe selection
lowerTimeframeTooltip = "The indicator scans lower timeframe data to approximate up and down volume used in the delta calculation. By default, the timeframe is chosen automatically. These inputs override this with a custom timeframe.
 \n\nHigher timeframes provide more historical data, but the data will be less precise."
useCustomTimeframeInput = input.bool(false, "Use custom timeframe", tooltip = lowerTimeframeTooltip)
lowerTimeframeInput = input.timeframe("1", "Timeframe")

// Delta intensity settings
intensityMultiplier = input.float(1.0, "Delta Sensitivity", minval=0.1, maxval=5.0, step=0.1, tooltip="Adjust the sensitivity of delta detection. Higher values make smaller deltas more visible.")

// Divergence settings
showDivergence = input.bool(true, "Show Divergence Background", tooltip="Show white background when candle direction conflicts with volume delta")
divergenceTransparency = input.int(80, "Divergence Background Transparency", minval=0, maxval=95, step=5, tooltip="Transparency level for divergence background (0=opaque, 95=very transparent)")

// Color customization settings
bullishColorGroup = "Bullish Colors (Positive Delta)"
veryLightBullishColor = input.color(color.new(color.blue, 85), "Very Light (0-25%)", group=bullishColorGroup)
lightBullishColor = input.color(color.new(color.blue, 65), "Light (25-50%)", group=bullishColorGroup)
mediumBullishColor = input.color(color.new(color.blue, 30), "Normal (50-75%)", group=bullishColorGroup)
darkBullishColor = input.color(color.new(color.blue, 0), "Dark (75%+)", group=bullishColorGroup)

bearishColorGroup = "Bearish Colors (Negative Delta)"
veryLightBearishColor = input.color(color.new(color.red, 85), "Very Light (0-25%)", group=bearishColorGroup)
lightBearishColor = input.color(color.new(color.red, 65), "Light (25-50%)", group=bearishColorGroup)
mediumBearishColor = input.color(color.new(color.red, 30), "Normal (50-75%)", group=bearishColorGroup)
darkBearishColor = input.color(color.new(color.red, 0), "Dark (75%+)", group=bearishColorGroup)

// Delta display settings
deltaDisplayGroup = "Delta Value Display"
showDeltaValue = input.bool(true, "Show Delta Values", group=deltaDisplayGroup, tooltip="Display delta values as text on chart")
deltaTextSize = input.string("tiny", "Delta Text Size", options=["tiny", "small", "normal", "large"], group=deltaDisplayGroup)
deltaTextColor = input.color(color.white, "Delta Text Color", group=deltaDisplayGroup)

// Color threshold display (for reference)
thresholdInfo = "Color Thresholds (based on Standard Deviation):\n• <25%: Very Light Blue/Red\n• 25-50%: Light Blue/Red (darker)\n• 50-75%: Normal Blue/Red\n• >75%: Dark Blue/Red\n\nSeparate calculations for bullish and bearish deltas"
showThresholds = input.bool(false, "Show Color Thresholds", tooltip=thresholdInfo)

// Determine the appropriate lower timeframe
var lowerTimeframe = switch
    useCustomTimeframeInput => lowerTimeframeInput
    timeframe.isseconds     => "1S"
    timeframe.isintraday    => "1"
    timeframe.isdaily       => "5"
    => "60"

// Get volume delta data
[openVolume, maxVolume, minVolume, lastVolume] = ta.requestVolumeDelta(lowerTimeframe)

// Length for standard deviation calculation
lookbackLength = input.int(100, "Lookback Length", minval=20, maxval=500, tooltip="Number of bars to use for standard deviation calculation")

// Separate positive and negative deltas for individual standard deviation calculations
positiveDelta = lastVolume > 0 ? lastVolume : 0
negativeDelta = lastVolume < 0 ? math.abs(lastVolume) : 0

// Calculate standard deviations for positive and negative deltas separately
positiveStdDev = ta.stdev(positiveDelta, lookbackLength)
negativeStdDev = ta.stdev(negativeDelta, lookbackLength)

// Calculate means for positive and negative deltas
positiveMean = ta.sma(positiveDelta, lookbackLength)
negativeMean = ta.sma(negativeDelta, lookbackLength)

// Normalize current delta based on its respective distribution
normalizedDelta = if lastVolume > 0 and positiveStdDev > 0
    (lastVolume - positiveMean) / positiveStdDev
else if lastVolume < 0 and negativeStdDev > 0
    (math.abs(lastVolume) - negativeMean) / negativeStdDev
else
    0

// Apply intensity multiplier and clamp to reasonable range
adjustedNormalizedDelta = math.abs(normalizedDelta * intensityMultiplier)
clampedDelta = math.min(adjustedNormalizedDelta, 3.0)  // Clamp to 3 standard deviations

// Function to get color based on normalized standard deviation
getColorByStdDev(normalizedStdDev, isPositive) =>
    // Convert normalized std dev to percentage thresholds
    // 0.5 std dev = 25%, 1.0 std dev = 50%, 1.5 std dev = 75%, 2.0+ std dev = 100%
    stdDevPercent = normalizedStdDev / 2.0  // Scale to make thresholds more reasonable

    if stdDevPercent < 0.25
        isPositive ? veryLightBullishColor : veryLightBearishColor  // Under 25% = very light
    else if stdDevPercent < 0.50
        isPositive ? lightBullishColor : lightBearishColor  // 25-50% = light (darker than under 25%)
    else if stdDevPercent < 0.75
        isPositive ? mediumBullishColor : mediumBearishColor  // 50-75% = normal blue/red
    else
        isPositive ? darkBullishColor : darkBearishColor  // 75%+ = dark blue/red

// Detect divergence between candle direction and volume delta
isBullishCandle = close > open
isBearishCandle = close < open
hasPositiveDelta = lastVolume > 0
hasNegativeDelta = lastVolume < 0

// Divergence occurs when:
// 1. Bullish candle (green) but negative delta (selling pressure)
// 2. Bearish candle (red) but positive delta (buying pressure)
isDivergence = (isBullishCandle and hasNegativeDelta) or (isBearishCandle and hasPositiveDelta)

// Determine candle color based on normalized standard deviation
candleColor = getColorByStdDev(clampedDelta, lastVolume > 0)

// Determine background color - white for divergence, transparent otherwise
backgroundColor = (isDivergence and showDivergence) ? color.new(color.white, divergenceTransparency) : na

// Plot the colored candles
plotcandle(open, high, low, close, "Delta Heatmap", color=candleColor, wickcolor=candleColor, bordercolor=candleColor)

// Plot background color for divergence
bgcolor(backgroundColor, title="Divergence Background")

// Error handling for missing volume data
var cumVol = 0.
cumVol += nz(volume)
if barstate.islast and cumVol == 0
    runtime.error("The data vendor doesn't provide volume data for this symbol.")

// Convert text size string to size constant
getTextSize(sizeStr) =>
    switch sizeStr
        "tiny" => size.tiny
        "small" => size.small
        "normal" => size.normal
        "large" => size.large
        => size.tiny

// Display delta values if enabled
if showDeltaValue and not na(lastVolume)
    deltaText = str.tostring(math.round(lastVolume), "#,###")
    label.new(bar_index, high, deltaText,
              style=label.style_none,
              textcolor=deltaTextColor,
              size=getTextSize(deltaTextSize),
              yloc=yloc.abovebar)