//@version=6
indicator("Delta Heatmap Candles", overlay=true, max_labels_count=500, max_lines_count=500)

import TradingView/ta/8

// Delta intensity settings
intensityMultiplier = input.float(1.0, "Delta Sensitivity", minval=0.1, maxval=5.0, step=0.1, tooltip="Adjust the sensitivity of delta detection. Higher values make smaller deltas more visible.")

// Standard deviation calculation settings
stdDevGroup = "Standard Deviation Calculation"
stdDevMode = input.string("All Data", "Calculation Mode", options=["All Data", "1 Hour", "15 Minutes", "5 Minutes", "1 Minute"], group=stdDevGroup, tooltip="Choose the timeframe for standard deviation calculation")
resetPeriod = input.int(24, "Reset Period (Hours)", minval=1, maxval=168, group=stdDevGroup, tooltip="Reset statistics every X hours (only for time-based modes)")



// Color customization settings
bullishColorGroup = "Bullish Colors (Positive Delta)"
veryLightBullishColor = input.color(#90bff9, "Very Light (0-25%)", group=bullishColorGroup)
lightBullishColor = input.color(#5b9cf6, "Light (25-50%)", group=bullishColorGroup)
mediumBullishColor = input.color(#1848cc, "Normal (50-75%)", group=bullishColorGroup)
darkBullishColor = input.color(#0c3299, "Dark (75%+)", group=bullishColorGroup)

bearishColorGroup = "Bearish Colors (Negative Delta)"
veryLightBearishColor = input.color(#faa1a4, "Very Light (0-25%)", group=bearishColorGroup)
lightBearishColor = input.color(#f77c80, "Light (25-50%)", group=bearishColorGroup)
mediumBearishColor = input.color(#f23645, "Normal (50-75%)", group=bearishColorGroup)
darkBearishColor = input.color(#801922, "Dark (75%+)", group=bearishColorGroup)

// Delta Volume Profile settings
dvpGroup = "Delta Volume Profile"
showDeltaVP = input.bool(false, "Show Delta Volume Profile", group=dvpGroup, tooltip="Display volume profile based on volume delta")
dvpLookback = input.int(200, "Lookback Bars", minval=10, maxval=1000, group=dvpGroup, tooltip="Number of bars to include in delta volume profile calculation")
dvpNumBars = input.int(50, "Number of Profile Bars", minval=10, maxval=100, group=dvpGroup, tooltip="Number of bars in the delta volume profile")
dvpBarThickness = input.int(2, "Bar Thickness", minval=1, maxval=10, group=dvpGroup, tooltip="Thickness of delta volume profile bars")
dvpRightOffset = input.int(50, "Right Offset", minval=0, maxval=200, group=dvpGroup, tooltip="Distance from right edge of chart")
dvpPositiveColor = input.color(color.new(#1848cc, 30), "Positive Delta Color", group=dvpGroup, tooltip="Color for positive delta volume bars")
dvpNegativeColor = input.color(color.new(#f23645, 30), "Negative Delta Color", group=dvpGroup, tooltip="Color for negative delta volume bars")
dvpShowPOC = input.bool(true, "Show Point of Control", group=dvpGroup, tooltip="Show the price level with highest delta volume")
dvpPOCColor = input.color(color.yellow, "POC Color", group=dvpGroup, tooltip="Color for Point of Control line")



// Color threshold display (for reference)
thresholdInfo = "Color Thresholds (based on Standard Deviation):\n• <25%: Very Light Blue/Red\n• 25-50%: Light Blue/Red (darker)\n• 50-75%: Normal Blue/Red\n• >75%: Dark Blue/Red\n\nCalculations use all available data"
showThresholds = input.bool(false, "Show Color Thresholds", tooltip=thresholdInfo)

// Determine the appropriate lower timeframe based on current chart timeframe
var lowerTimeframe = switch
    timeframe.isseconds     => "1S"
    timeframe.isintraday    => "1"
    timeframe.isdaily       => "5"
    => "60"

// Get volume delta data
[openVolume, maxVolume, minVolume, lastVolume] = ta.requestVolumeDelta(lowerTimeframe)

// Variables to track statistics
var float positiveDeltaSum = 0.0
var float negativeDeltaSum = 0.0
var float positiveDeltaSumSq = 0.0
var float negativeDeltaSumSq = 0.0
var int positiveCount = 0
var int negativeCount = 0
var int lastResetTime = 0

// Function to get reset interval in milliseconds
getResetInterval() =>
    switch stdDevMode
        "1 Hour" => 60 * 60 * 1000
        "15 Minutes" => 15 * 60 * 1000
        "5 Minutes" => 5 * 60 * 1000
        "1 Minute" => 1 * 60 * 1000
        => 0  // All Data - never reset

// Check if we need to reset statistics
resetInterval = getResetInterval()
currentTime = time
shouldReset = resetInterval > 0 and (currentTime - lastResetTime) >= resetInterval

// Reset statistics if needed
if shouldReset or (stdDevMode == "All Data" and lastResetTime == 0)
    positiveDeltaSum := 0.0
    negativeDeltaSum := 0.0
    positiveDeltaSumSq := 0.0
    negativeDeltaSumSq := 0.0
    positiveCount := 0
    negativeCount := 0
    lastResetTime := currentTime

// Update statistics with current bar data
if not na(lastVolume)
    if lastVolume > 0
        positiveDeltaSum += lastVolume
        positiveDeltaSumSq += lastVolume * lastVolume
        positiveCount += 1
    else if lastVolume < 0
        absNegativeDelta = math.abs(lastVolume)
        negativeDeltaSum += absNegativeDelta
        negativeDeltaSumSq += absNegativeDelta * absNegativeDelta
        negativeCount += 1

// Calculate means and standard deviations
positiveMean = positiveCount > 0 ? positiveDeltaSum / positiveCount : 0
negativeMean = negativeCount > 0 ? negativeDeltaSum / negativeCount : 0

positiveVariance = positiveCount > 1 ? (positiveDeltaSumSq - (positiveDeltaSum * positiveDeltaSum / positiveCount)) / (positiveCount - 1) : 0
negativeVariance = negativeCount > 1 ? (negativeDeltaSumSq - (negativeDeltaSum * negativeDeltaSum / negativeCount)) / (negativeCount - 1) : 0

positiveStdDev = positiveVariance > 0 ? math.sqrt(positiveVariance) : 0
negativeStdDev = negativeVariance > 0 ? math.sqrt(negativeVariance) : 0

// Normalize current delta based on its respective distribution
normalizedDelta = if lastVolume > 0 and positiveStdDev > 0
    (lastVolume - positiveMean) / positiveStdDev
else if lastVolume < 0 and negativeStdDev > 0
    (math.abs(lastVolume) - negativeMean) / negativeStdDev
else
    0

// Apply intensity multiplier and clamp to reasonable range
adjustedNormalizedDelta = math.abs(normalizedDelta * intensityMultiplier)
clampedDelta = math.min(adjustedNormalizedDelta, 3.0)  // Clamp to 3 standard deviations

// Function to get color based on normalized standard deviation
getColorByStdDev(normalizedStdDev, isPositive) =>
    // Convert normalized std dev to percentage thresholds
    // 0.5 std dev = 25%, 1.0 std dev = 50%, 1.5 std dev = 75%, 2.0+ std dev = 100%
    stdDevPercent = normalizedStdDev / 2.0  // Scale to make thresholds more reasonable

    if stdDevPercent < 0.25
        isPositive ? veryLightBullishColor : veryLightBearishColor  // Under 25% = very light
    else if stdDevPercent < 0.50
        isPositive ? lightBullishColor : lightBearishColor  // 25-50% = light (darker than under 25%)
    else if stdDevPercent < 0.75
        isPositive ? mediumBullishColor : mediumBearishColor  // 50-75% = normal blue/red
    else
        isPositive ? darkBullishColor : darkBearishColor  // 75%+ = dark blue/red



// Determine candle color based on normalized standard deviation
candleColor = getColorByStdDev(clampedDelta, lastVolume > 0)

// Color the existing candles
barcolor(candleColor, title="Delta Heatmap")

// Delta Volume Profile variables and calculations
var array<line> dvpBars = array.new_line()
var array<float> dvpPositiveVolumes = array.new_float()
var array<float> dvpNegativeVolumes = array.new_float()
var line dvpPOCLine = na

// Initialize delta volume profile arrays
if barstate.isfirst and showDeltaVP
    array.clear(dvpBars)
    array.clear(dvpPositiveVolumes)
    array.clear(dvpNegativeVolumes)

    // Initialize arrays with zeros
    for i = 0 to dvpNumBars - 1
        array.push(dvpPositiveVolumes, 0.0)
        array.push(dvpNegativeVolumes, 0.0)
        array.push(dvpBars, line.new(x1=bar_index, y1=close, x2=bar_index, y2=close, width=dvpBarThickness))

    // Initialize POC line
    if dvpShowPOC
        dvpPOCLine := line.new(x1=bar_index, y1=close, x2=bar_index, y2=close, color=dvpPOCColor, width=dvpBarThickness)

// Calculate delta volume profile
calculateDeltaVP() =>
    if not showDeltaVP
        return

    // Reset arrays
    for i = 0 to array.size(dvpPositiveVolumes) - 1
        array.set(dvpPositiveVolumes, i, 0.0)
        array.set(dvpNegativeVolumes, i, 0.0)

    // Get price range for the lookback period
    highestPrice = ta.highest(high, dvpLookback)
    lowestPrice = ta.lowest(low, dvpLookback)
    priceInterval = (highestPrice - lowestPrice) / (dvpNumBars - 1)

    // Calculate delta volume for each price level
    for i = 0 to dvpLookback - 1
        deltaVol = ta.requestVolumeDelta(timeframe.period)[i]
        if not na(deltaVol)
            for j = 0 to dvpNumBars - 1
                priceLevel = lowestPrice + priceInterval * j
                if priceLevel >= low[i] and priceLevel <= high[i]
                    if deltaVol > 0
                        array.set(dvpPositiveVolumes, j, array.get(dvpPositiveVolumes, j) + deltaVol)
                    else
                        array.set(dvpNegativeVolumes, j, array.get(dvpNegativeVolumes, j) + math.abs(deltaVol))

// Draw delta volume profile
drawDeltaVP() =>
    if not showDeltaVP
        return

    // Get price range
    highestPrice = ta.highest(high, dvpLookback)
    lowestPrice = ta.lowest(low, dvpLookback)
    priceInterval = (highestPrice - lowestPrice) / (dvpNumBars - 1)

    // Find maximum volumes for scaling
    maxPositiveVol = array.max(dvpPositiveVolumes)
    maxNegativeVol = array.max(dvpNegativeVolumes)
    maxVol = math.max(maxPositiveVol, maxNegativeVol)

    // Find POC (Point of Control)
    maxTotalVol = 0.0
    pocIndex = 0
    for i = 0 to dvpNumBars - 1
        totalVol = array.get(dvpPositiveVolumes, i) + array.get(dvpNegativeVolumes, i)
        if totalVol > maxTotalVol
            maxTotalVol := totalVol
            pocIndex := i

    // Draw volume bars
    x2 = bar_index + dvpRightOffset
    for i = 0 to dvpNumBars - 1
        positiveVol = array.get(dvpPositiveVolumes, i)
        negativeVol = array.get(dvpNegativeVolumes, i)
        totalVol = positiveVol + negativeVol

        if totalVol > 0
            // Scale volume for display
            scaledVol = math.round((totalVol / maxVol) * 30)  // Scale to max 30 bars width
            x1 = x2 - scaledVol
            priceLevel = lowestPrice + priceInterval * i

            // Determine color based on dominant delta
            barColor = positiveVol > negativeVol ? dvpPositiveColor : dvpNegativeColor

            // Update line position and color
            line.set_xy1(array.get(dvpBars, i), x1, priceLevel)
            line.set_xy2(array.get(dvpBars, i), x2, priceLevel)
            line.set_color(array.get(dvpBars, i), barColor)
        else
            // Hide bars with no volume
            line.set_xy1(array.get(dvpBars, i), x2, lowestPrice + priceInterval * i)
            line.set_xy2(array.get(dvpBars, i), x2, lowestPrice + priceInterval * i)

    // Draw POC line
    if dvpShowPOC and not na(dvpPOCLine)
        pocPrice = lowestPrice + priceInterval * pocIndex
        pocX1 = bar_index - dvpLookback
        pocX2 = x2 + 10
        line.set_xy1(dvpPOCLine, pocX1, pocPrice)
        line.set_xy2(dvpPOCLine, pocX2, pocPrice)

// Execute delta volume profile calculations on last bar
if barstate.islast and showDeltaVP
    calculateDeltaVP()
    drawDeltaVP()



// Error handling for missing volume data
var cumVol = 0.
cumVol += nz(volume)
if barstate.islast and cumVol == 0
    runtime.error("The data vendor doesn't provide volume data for this symbol.")

