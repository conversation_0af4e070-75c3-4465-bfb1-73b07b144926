//@version=6
indicator("Delta Heatmap Candles", overlay=true)

import TradingView/ta/8

// Input settings for timeframe selection
lowerTimeframeTooltip = "The indicator scans lower timeframe data to approximate up and down volume used in the delta calculation. By default, the timeframe is chosen automatically. These inputs override this with a custom timeframe.
 \n\nHigher timeframes provide more historical data, but the data will be less precise."
useCustomTimeframeInput = input.bool(false, "Use custom timeframe", tooltip = lowerTimeframeTooltip)
lowerTimeframeInput = input.timeframe("1", "Timeframe")

// Delta intensity settings
intensityMultiplier = input.float(1.0, "Delta Sensitivity", minval=0.1, maxval=5.0, step=0.1, tooltip="Adjust the sensitivity of delta detection. Higher values make smaller deltas more visible.")

// Divergence settings
showDivergence = input.bool(true, "Show Divergence Background", tooltip="Show white background when candle direction conflicts with volume delta")
divergenceTransparency = input.int(80, "Divergence Background Transparency", minval=0, maxval=95, step=5, tooltip="Transparency level for divergence background (0=opaque, 95=very transparent)")

// Color threshold display (for reference)
thresholdInfo = "Color Thresholds (based on Standard Deviation):\n• <25%: Very Light Blue/Red\n• 25-50%: Light Blue/Red (darker)\n• 50-75%: Normal Blue/Red\n• >75%: Dark Blue/Red\n\nSeparate calculations for bullish and bearish deltas"
showThresholds = input.bool(false, "Show Color Thresholds", tooltip=thresholdInfo)

// Determine the appropriate lower timeframe
var lowerTimeframe = switch
    useCustomTimeframeInput => lowerTimeframeInput
    timeframe.isseconds     => "1S"
    timeframe.isintraday    => "1"
    timeframe.isdaily       => "5"
    => "60"

// Get volume delta data
[openVolume, maxVolume, minVolume, lastVolume] = ta.requestVolumeDelta(lowerTimeframe)

// Length for standard deviation calculation
lookbackLength = input.int(100, "Lookback Length", minval=20, maxval=500, tooltip="Number of bars to use for standard deviation calculation")

// Separate positive and negative deltas for individual standard deviation calculations
positiveDelta = lastVolume > 0 ? lastVolume : 0
negativeDelta = lastVolume < 0 ? math.abs(lastVolume) : 0

// Calculate standard deviations for positive and negative deltas separately
positiveStdDev = ta.stdev(positiveDelta, lookbackLength)
negativeStdDev = ta.stdev(negativeDelta, lookbackLength)

// Calculate means for positive and negative deltas
positiveMean = ta.sma(positiveDelta, lookbackLength)
negativeMean = ta.sma(negativeDelta, lookbackLength)

// Normalize current delta based on its respective distribution
normalizedDelta = if lastVolume > 0 and positiveStdDev > 0
    (lastVolume - positiveMean) / positiveStdDev
else if lastVolume < 0 and negativeStdDev > 0
    (math.abs(lastVolume) - negativeMean) / negativeStdDev
else
    0

// Apply intensity multiplier and clamp to reasonable range
adjustedNormalizedDelta = math.abs(normalizedDelta * intensityMultiplier)
clampedDelta = math.min(adjustedNormalizedDelta, 3.0)  // Clamp to 3 standard deviations

// Define color thresholds and colors
// Bullish colors (positive delta)
veryLightBlue = color.new(color.blue, 85)    // Very light blue (0-25%)
lightBlue = color.new(color.blue, 65)       // Light blue (25-50%)
mediumBlue = color.new(color.blue, 30)      // Normal blue (50-75%)
darkBlue = color.new(color.blue, 0)         // Dark blue (75%+)

// Bearish colors (negative delta)
veryLightRed = color.new(color.red, 85)     // Very light red (0-25%)
lightRed = color.new(color.red, 65)        // Light red (25-50%)
mediumRed = color.new(color.red, 30)       // Normal red (50-75%)
darkRed = color.new(color.red, 0)          // Dark red (75%+)

// Function to get color based on normalized standard deviation
getColorByStdDev(normalizedStdDev, isPositive) =>
    // Convert normalized std dev to percentage thresholds
    // 0.5 std dev = 25%, 1.0 std dev = 50%, 1.5 std dev = 75%, 2.0+ std dev = 100%
    stdDevPercent = normalizedStdDev / 2.0  // Scale to make thresholds more reasonable

    if stdDevPercent < 0.25
        isPositive ? veryLightBlue : veryLightRed  // Under 25% = very light
    else if stdDevPercent < 0.50
        isPositive ? lightBlue : lightRed  // 25-50% = light (darker than under 25%)
    else if stdDevPercent < 0.75
        isPositive ? mediumBlue : mediumRed  // 50-75% = normal blue/red
    else
        isPositive ? darkBlue : darkRed  // 75%+ = dark blue/red

// Detect divergence between candle direction and volume delta
isBullishCandle = close > open
isBearishCandle = close < open
hasPositiveDelta = lastVolume > 0
hasNegativeDelta = lastVolume < 0

// Divergence occurs when:
// 1. Bullish candle (green) but negative delta (selling pressure)
// 2. Bearish candle (red) but positive delta (buying pressure)
isDivergence = (isBullishCandle and hasNegativeDelta) or (isBearishCandle and hasPositiveDelta)

// Determine candle color based on normalized standard deviation
candleColor = getColorByStdDev(clampedDelta, lastVolume > 0)

// Determine background color - white for divergence, transparent otherwise
backgroundColor = (isDivergence and showDivergence) ? color.new(color.white, divergenceTransparency) : na

// Plot the colored candles
plotcandle(open, high, low, close, "Delta Heatmap", color=candleColor, wickcolor=candleColor, bordercolor=candleColor)

// Plot background color for divergence
bgcolor(backgroundColor, title="Divergence Background")

// Error handling for missing volume data
var cumVol = 0.
cumVol += nz(volume)
if barstate.islast and cumVol == 0
    runtime.error("The data vendor doesn't provide volume data for this symbol.")

// Optional: Add a small indicator to show delta value
plotchar(lastVolume, "Delta Value", "", location.top, size=size.tiny)