//@version=6
indicator("Delta Heatmap Candles", overlay=true)

import TradingView/ta/8

// Delta intensity settings
intensityMultiplier = input.float(1.0, "Delta Sensitivity", minval=0.1, maxval=5.0, step=0.1, tooltip="Adjust the sensitivity of delta detection. Higher values make smaller deltas more visible.")

// Divergence settings
showDivergence = input.bool(true, "Show Divergence Signals", tooltip="Show triangular markers when candle direction conflicts with volume delta")
divergenceColor = input.color(color.white, "Divergence Signal Color", tooltip="Color for divergence triangle markers")
divergenceSize = input.string("small", "Divergence Signal Size", options=["tiny", "small", "normal", "large"], tooltip="Size of divergence triangle markers")

// Color customization settings
bullishColorGroup = "Bullish Colors (Positive Delta)"
veryLightBullishColor = input.color(color.new(color.blue, 85), "Very Light (0-25%)", group=bullishColorGroup)
lightBullishColor = input.color(color.new(color.blue, 65), "Light (25-50%)", group=bullishColorGroup)
mediumBullishColor = input.color(color.new(color.blue, 30), "Normal (50-75%)", group=bullishColorGroup)
darkBullishColor = input.color(color.new(color.blue, 0), "Dark (75%+)", group=bullishColorGroup)

bearishColorGroup = "Bearish Colors (Negative Delta)"
veryLightBearishColor = input.color(color.new(color.red, 85), "Very Light (0-25%)", group=bearishColorGroup)
lightBearishColor = input.color(color.new(color.red, 65), "Light (25-50%)", group=bearishColorGroup)
mediumBearishColor = input.color(color.new(color.red, 30), "Normal (50-75%)", group=bearishColorGroup)
darkBearishColor = input.color(color.new(color.red, 0), "Dark (75%+)", group=bearishColorGroup)

// Delta display settings
deltaDisplayGroup = "Delta Value Display"
showDeltaValue = input.bool(true, "Show Delta Values", group=deltaDisplayGroup, tooltip="Display delta values as text on chart")
deltaTextSize = input.string("tiny", "Delta Text Size", options=["tiny", "small", "normal", "large"], group=deltaDisplayGroup)
deltaTextColor = input.color(color.white, "Delta Text Color", group=deltaDisplayGroup)

// Color threshold display (for reference)
thresholdInfo = "Color Thresholds (based on Standard Deviation):\n• <25%: Very Light Blue/Red\n• 25-50%: Light Blue/Red (darker)\n• 50-75%: Normal Blue/Red\n• >75%: Dark Blue/Red\n\nCalculations use all available data"
showThresholds = input.bool(false, "Show Color Thresholds", tooltip=thresholdInfo)

// Determine the appropriate lower timeframe based on current chart timeframe
var lowerTimeframe = switch
    timeframe.isseconds     => "1S"
    timeframe.isintraday    => "1"
    timeframe.isdaily       => "5"
    => "60"

// Get volume delta data
[openVolume, maxVolume, minVolume, lastVolume] = ta.requestVolumeDelta(lowerTimeframe)

// Variables to track all-time statistics
var float positiveDeltaSum = 0.0
var float negativeDeltaSum = 0.0
var float positiveDeltaSumSq = 0.0
var float negativeDeltaSumSq = 0.0
var int positiveCount = 0
var int negativeCount = 0

// Update statistics with current bar data
if not na(lastVolume)
    if lastVolume > 0
        positiveDeltaSum += lastVolume
        positiveDeltaSumSq += lastVolume * lastVolume
        positiveCount += 1
    else if lastVolume < 0
        absNegativeDelta = math.abs(lastVolume)
        negativeDeltaSum += absNegativeDelta
        negativeDeltaSumSq += absNegativeDelta * absNegativeDelta
        negativeCount += 1

// Calculate means and standard deviations using all available data
positiveMean = positiveCount > 0 ? positiveDeltaSum / positiveCount : 0
negativeMean = negativeCount > 0 ? negativeDeltaSum / negativeCount : 0

positiveVariance = positiveCount > 1 ? (positiveDeltaSumSq - (positiveDeltaSum * positiveDeltaSum / positiveCount)) / (positiveCount - 1) : 0
negativeVariance = negativeCount > 1 ? (negativeDeltaSumSq - (negativeDeltaSum * negativeDeltaSum / negativeCount)) / (negativeCount - 1) : 0

positiveStdDev = positiveVariance > 0 ? math.sqrt(positiveVariance) : 0
negativeStdDev = negativeVariance > 0 ? math.sqrt(negativeVariance) : 0

// Normalize current delta based on its respective distribution
normalizedDelta = if lastVolume > 0 and positiveStdDev > 0
    (lastVolume - positiveMean) / positiveStdDev
else if lastVolume < 0 and negativeStdDev > 0
    (math.abs(lastVolume) - negativeMean) / negativeStdDev
else
    0

// Apply intensity multiplier and clamp to reasonable range
adjustedNormalizedDelta = math.abs(normalizedDelta * intensityMultiplier)
clampedDelta = math.min(adjustedNormalizedDelta, 3.0)  // Clamp to 3 standard deviations

// Function to get color based on normalized standard deviation
getColorByStdDev(normalizedStdDev, isPositive) =>
    // Convert normalized std dev to percentage thresholds
    // 0.5 std dev = 25%, 1.0 std dev = 50%, 1.5 std dev = 75%, 2.0+ std dev = 100%
    stdDevPercent = normalizedStdDev / 2.0  // Scale to make thresholds more reasonable

    if stdDevPercent < 0.25
        isPositive ? veryLightBullishColor : veryLightBearishColor  // Under 25% = very light
    else if stdDevPercent < 0.50
        isPositive ? lightBullishColor : lightBearishColor  // 25-50% = light (darker than under 25%)
    else if stdDevPercent < 0.75
        isPositive ? mediumBullishColor : mediumBearishColor  // 50-75% = normal blue/red
    else
        isPositive ? darkBullishColor : darkBearishColor  // 75%+ = dark blue/red

// Detect divergence between candle direction and volume delta
isBullishCandle = close > open
isBearishCandle = close < open
hasPositiveDelta = lastVolume > 0
hasNegativeDelta = lastVolume < 0

// Specific divergence types:
// 1. Bullish candle but negative delta (selling pressure) - show inverted triangle above
// 2. Bearish candle but positive delta (buying pressure) - show triangle below
bullishCandleBearishDelta = isBullishCandle and hasNegativeDelta
bearishCandleBullishDelta = isBearishCandle and hasPositiveDelta

// Determine candle color based on normalized standard deviation
candleColor = getColorByStdDev(clampedDelta, lastVolume > 0)

// Function to get marker size
getMarkerSize(sizeStr) =>
    switch sizeStr
        "tiny" => size.tiny
        "small" => size.small
        "normal" => size.normal
        "large" => size.large
        => size.small

// Plot the colored candles
plotcandle(open, high, low, close, "Delta Heatmap", color=candleColor, wickcolor=candleColor, bordercolor=candleColor)

// Plot divergence signals
if showDivergence
    // Inverted triangle above bullish candle with bearish delta
    if bullishCandleBearishDelta
        plotshape(high, title="Bullish Candle Bearish Delta", style=shape.triangledown, location=location.abovebar, color=divergenceColor, size=getMarkerSize(divergenceSize))

    // Normal triangle below bearish candle with bullish delta
    if bearishCandleBullishDelta
        plotshape(low, title="Bearish Candle Bullish Delta", style=shape.triangleup, location=location.belowbar, color=divergenceColor, size=getMarkerSize(divergenceSize))

// Error handling for missing volume data
var cumVol = 0.
cumVol += nz(volume)
if barstate.islast and cumVol == 0
    runtime.error("The data vendor doesn't provide volume data for this symbol.")

// Convert text size string to size constant
getTextSize(sizeStr) =>
    switch sizeStr
        "tiny" => size.tiny
        "small" => size.small
        "normal" => size.normal
        "large" => size.large
        => size.tiny

// Display delta values if enabled
if showDeltaValue
    deltaValue = na(lastVolume) ? 0 : lastVolume
    deltaText = str.tostring(math.round(deltaValue), "#,###")
    label.new(bar_index, high, deltaText,
              style=label.style_none,
              textcolor=deltaTextColor,
              size=getTextSize(deltaTextSize),
              yloc=yloc.abovebar)