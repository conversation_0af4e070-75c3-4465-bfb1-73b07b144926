//@version=6
indicator("Delta Heatmap Candles", overlay=true)

import TradingView/ta/8

// Input settings for timeframe selection
lowerTimeframeTooltip = "The indicator scans lower timeframe data to approximate up and down volume used in the delta calculation. By default, the timeframe is chosen automatically. These inputs override this with a custom timeframe.
 \n\nHigher timeframes provide more historical data, but the data will be less precise."
useCustomTimeframeInput = input.bool(false, "Use custom timeframe", tooltip = lowerTimeframeTooltip)
lowerTimeframeInput = input.timeframe("1", "Timeframe")

// Gradient intensity settings
intensityMultiplier = input.float(1.0, "Intensity Multiplier", minval=0.1, maxval=5.0, step=0.1, tooltip="Adjust the intensity of the gradient colors")
showNeutralCandles = input.bool(true, "Show Neutral Candles", tooltip="Show regular candles when delta is near zero")
neutralThreshold = input.float(0.1, "Neutral Threshold", minval=0.01, maxval=1.0, step=0.01, tooltip="Delta percentage threshold for neutral candles")

// Determine the appropriate lower timeframe
var lowerTimeframe = switch
    useCustomTimeframeInput => lowerTimeframeInput
    timeframe.isseconds     => "1S"
    timeframe.isintraday    => "1"
    timeframe.isdaily       => "5"
    => "60"

// Get volume delta data
[openVolume, maxVolume, minVolume, lastVolume] = ta.requestVolumeDelta(lowerTimeframe)

// Calculate delta percentage relative to total volume
totalVolume = math.abs(lastVolume) * 2  // Approximate total volume
deltaPercentage = totalVolume > 0 ? math.abs(lastVolume) / totalVolume : 0

// Apply intensity multiplier
adjustedIntensity = math.min(deltaPercentage * intensityMultiplier, 1.0)

// Define base colors
positiveBaseColor = color.blue
negativeBaseColor = color.red
neutralColor = color.gray

// Create gradient colors based on delta intensity
getGradientColor(baseColor, intensity) =>
    color.new(baseColor, 100 - (intensity * 80))  // 20% minimum opacity, 100% maximum

// Determine candle color
candleColor = if math.abs(deltaPercentage) < neutralThreshold and showNeutralCandles
    neutralColor
else if lastVolume > 0
    getGradientColor(positiveBaseColor, adjustedIntensity)
else
    getGradientColor(negativeBaseColor, adjustedIntensity)

// Plot the colored candles
plotcandle(open, high, low, close, "Delta Heatmap", color=candleColor, wickcolor=candleColor, bordercolor=candleColor)

// Error handling for missing volume data
var cumVol = 0.
cumVol += nz(volume)
if barstate.islast and cumVol == 0
    runtime.error("The data vendor doesn't provide volume data for this symbol.")

// Optional: Add a small indicator to show delta value
plotchar(lastVolume, "Delta Value", "", location.top, size=size.tiny)